import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../api/api_service.dart';
import '../api/endpoints.dart';
import '../api/models/user_model.dart';
import '../models/bank.dart';
import '../models/state.dart' as app_state;
import '../models/dva_info.dart';
import '../models/delivery_summary.dart';
import '../models/delivery.dart';
import '../models/notification.dart';
import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;

class AuthProvider extends ChangeNotifier {
  ApiService _apiService = ApiService();

  // Setter for injecting mock API service in tests
  set apiService(ApiService service) {
    _apiService = service;
  }

  User? _user;
  bool _isLoading = false;
  String? _error;
  final List<Bank> _banks = [];
  bool _isLoadingBanks = false;
  String? _banksError;
  final List<app_state.State> _states = [];
  bool _isLoadingStates = false;
  String? _statesError;
  DvaInfo? _dvaInfo;
  bool _isLoadingDva = false;
  String? _dvaError;
  DeliverySummary? _deliverySummary;
  bool _isLoadingDeliverySummary = false;
  String? _deliverySummaryError;
  final List<Delivery> _deliveries = [];
  bool _isLoadingDeliveries = false;
  String? _deliveriesError;
  final List<NotificationModel> _notifications = [];
  bool _isLoadingNotifications = false;
  String? _notificationsError;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;
  List<Bank> get banks => _banks;
  bool get isLoadingBanks => _isLoadingBanks;
  String? get banksError => _banksError;
  List<app_state.State> get states => _states;
  bool get isLoadingStates => _isLoadingStates;
  String? get statesError => _statesError;
  DvaInfo? get dvaInfo => _dvaInfo;
  bool get isLoadingDva => _isLoadingDva;
  String? get dvaError => _dvaError;
  DeliverySummary? get deliverySummary => _deliverySummary;
  bool get isLoadingDeliverySummary => _isLoadingDeliverySummary;
  String? get deliverySummaryError => _deliverySummaryError;
  List<Delivery> get deliveries => _deliveries;
  bool get isLoadingDeliveries => _isLoadingDeliveries;
  String? get deliveriesError => _deliveriesError;
  List<NotificationModel> get notifications => _notifications;
  bool get isLoadingNotifications => _isLoadingNotifications;
  String? get notificationsError => _notificationsError;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _setLoadingBanks(bool loading) {
    _isLoadingBanks = loading;
    notifyListeners();
  }

  void _setBanksError(String? error) {
    _banksError = error;
    notifyListeners();
  }

  void _setLoadingStates(bool loading) {
    _isLoadingStates = loading;
    notifyListeners();
  }

  void _setStatesError(String? error) {
    _statesError = error;
    notifyListeners();
  }

  void _setLoadingDva(bool loading) {
    _isLoadingDva = loading;
    notifyListeners();
  }

  void _setDvaError(String? error) {
    _dvaError = error;
    notifyListeners();
  }

  void _setLoadingDeliverySummary(bool loading) {
    _isLoadingDeliverySummary = loading;
    notifyListeners();
  }

  void _setDeliverySummaryError(String? error) {
    _deliverySummaryError = error;
    notifyListeners();
  }

  void _setLoadingDeliveries(bool loading) {
    _isLoadingDeliveries = loading;
    notifyListeners();
  }

  void _setDeliveriesError(String? error) {
    _deliveriesError = error;
    notifyListeners();
  }

  void _setLoadingNotifications(bool loading) {
    _isLoadingNotifications = loading;
    notifyListeners();
  }

  void _setNotificationsError(String? error) {
    _notificationsError = error;
    notifyListeners();
  }

  // Check if user is authenticated with a valid token
  Future<bool> checkAuthentication() async {
    developer.log('Checking authentication status', name: 'auth_provider');

    _setLoading(true);

    try {
      // First check if we have a valid token
      final token = await _apiService.getToken();

      if (token == null) {
        developer.log('No token found or token expired', name: 'auth_provider');
        _setLoading(false);
        return false;
      }

      // Check if we have cached user data
      final userData = await _apiService.getUserData();
      if (userData != null) {
        developer.log('Found cached user data', name: 'auth_provider');

        // Try to create user from cached data
        try {
          final userId = userData['userId'];
          final userRole = userData['userRole'];
          final email = userData['email'];

          if (userId != null && userRole != null) {
            _user = User(userId: userId, userRole: userRole, email: email);

            // Validate the token with the server
            final response = await _apiService.get(
              ApiEndpoints.userProfile,
              requiresAuth: true,
            );

            if (response.success && response.data != null) {
              developer.log('Token validated with server',
                  name: 'auth_provider');

              // Update user data if needed
              if (response.data['data'] != null) {
                final serverUserData = response.data['data'];
                // Update user data in cache
                await _apiService.saveUserData(serverUserData);

                // Update user object if needed
                if (serverUserData['userId'] != null &&
                    serverUserData['userRole'] != null) {
                  _user = User(
                    userId: serverUserData['userId'],
                    userRole: serverUserData['userRole'],
                    email: serverUserData['email'],
                  );
                }
              }

              _setLoading(false);
              notifyListeners();
              return true;
            } else {
              // Token is invalid or expired
              developer.log('Token validation failed: ${response.error}',
                  name: 'auth_provider');
              await _apiService.clearToken();
              await _apiService.clearUserData();
              _user = null;
              _setLoading(false);
              notifyListeners();
              return false;
            }
          }
        } catch (e) {
          developer.log('Error restoring user from cached data: $e',
              name: 'auth_provider');
        }
      }

      // If we get here, we have a token but no valid user data
      // Try to get user profile from server
      final response = await _apiService.get(
        ApiEndpoints.userProfile,
        requiresAuth: true,
      );

      if (response.success &&
          response.data != null &&
          response.data['data'] != null) {
        developer.log('Successfully retrieved user profile',
            name: 'auth_provider');

        final userData = response.data['data'];

        // Save user data to cache
        await _apiService.saveUserData(userData);

        // Create user object
        if (userData['userId'] != null && userData['userRole'] != null) {
          _user = User(
            userId: userData['userId'],
            userRole: userData['userRole'],
            email: userData['email'],
          );

          _setLoading(false);
          notifyListeners();
          return true;
        }
      }

      // If we get here, authentication failed
      await _apiService.clearToken();
      await _apiService.clearUserData();
      _user = null;
      _setLoading(false);
      return false;
    } catch (e) {
      developer.log('Error during authentication check: $e',
          name: 'auth_provider');
      _setLoading(false);
      return false;
    }
  }

  // Login a user
  Future<bool> login(String email, String password) async {
    developer.log('Starting user login', name: 'auth_provider');
    developer.log('Login data: email=$email', name: 'auth_provider');

    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.post(
        ApiEndpoints.login,
        body: {
          'email': email,
          'password': password,
        },
        requiresAuth: false,
      );

      developer.log('Login response received', name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');
      developer.log('Response error: ${response.error}', name: 'auth_provider');

      _setLoading(false);

      if (response.success && response.data != null) {
        developer.log('Login successful', name: 'auth_provider');

        // Extract JWT token from response data
        final jwt = response.data['data']?['jwt'];
        final userId = response.data['data']?['userId'];
        final userRole = response.data['data']?['userRole'];

        if (jwt != null && userId != null && userRole != null) {
          // Save token and update user state
          await _apiService.saveToken(jwt);

          // Create user object
          _user = User(userId: userId, userRole: userRole, email: email);

          // Save user data to cache for persistence
          await _apiService.saveUserData({
            'userId': userId,
            'userRole': userRole,
            'email': email,
          });

          notifyListeners();
          return true;
        } else {
          // Missing required data in response
          developer.log('Login failed: Missing required data in response',
              name: 'auth_provider');
          _setError('Invalid response from server. Please try again.');
          return false;
        }
      } else {
        // API returned an error
        developer.log('Login failed: ${response.error}', name: 'auth_provider');

        // Use the error message from the API response or a default message
        String errorMessage = response.error ?? 'Invalid credentials';

        // Check for specific error messages and make them more user-friendly
        if (errorMessage.contains('Validation failed')) {
          errorMessage = 'Please check your email and password and try again.';
        } else if (errorMessage.contains('not found') ||
            errorMessage.contains('incorrect')) {
          errorMessage = 'Invalid email or password.';
        }

        _setError(errorMessage);
        return false;
      }
    } on TimeoutException {
      _setLoading(false);
      _setError(
          'The server is taking too long to respond. Please try again later.');
      return false;
    } catch (e) {
      developer.log('Unexpected error during login: $e', name: 'auth_provider');
      _setLoading(false);
      _setError('An unexpected error occurred. Please try again.');
      return false;
    }
  }

  // Logout a user
  Future<Map<String, dynamic>> logout() async {
    developer.log('Starting user logout', name: 'auth_provider');

    _setLoading(true);
    _setError(null);

    try {
      // Check if we have a user with email
      if (_user?.email != null) {
        developer.log('Calling logout API with email: ${_user!.email}',
            name: 'auth_provider');

        // Call the logout API
        final response = await _apiService.post(
          ApiEndpoints.logout,
          body: {
            'email': _user!.email,
          },
          requiresAuth:
              true, // This will include the Authorization header with the token
        );

        developer.log('Logout response received', name: 'auth_provider');
        developer.log('Response success: ${response.success}',
            name: 'auth_provider');
        developer.log('Response data: ${response.data}', name: 'auth_provider');
        developer.log('Response error: ${response.error}',
            name: 'auth_provider');

        // Clear token, user data, and user state regardless of API response
        await _apiService.clearToken();
        await _apiService.clearUserData();
        _user = null;
        _setLoading(false);
        notifyListeners();

        if (response.success && response.data != null) {
          developer.log('Logout successful', name: 'auth_provider');
          return {
            'success': true,
            'message': response.data['responseMsg'] ?? 'Logout successful',
          };
        } else {
          // API returned an error, but we still logged out locally
          String errorMessage =
              response.error ?? 'Logout failed on server but completed locally';
          developer.log('Logout API error: $errorMessage',
              name: 'auth_provider');
          return {
            'success':
                true, // Still consider it a success since we logged out locally
            'message': 'Logged out locally. Server message: $errorMessage',
            'apiSuccess': false,
          };
        }
      } else {
        // No user email available, just clear local state
        developer.log('No user email available, performing local logout only',
            name: 'auth_provider');
        await _apiService.clearToken();
        await _apiService.clearUserData();
        _user = null;
        _setLoading(false);
        notifyListeners();

        return {
          'success': true,
          'message': 'Logged out locally',
          'apiCalled': false,
        };
      }
    } catch (e) {
      developer.log('Unexpected error during logout: $e',
          name: 'auth_provider');

      // Still clear token, user data, and user state to ensure local logout
      await _apiService.clearToken();
      await _apiService.clearUserData();
      _user = null;
      _setLoading(false);
      notifyListeners();

      return {
        'success':
            true, // Still consider it a success since we logged out locally
        'message': 'Logged out locally. Error: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  // Clear authentication data for users pending admin approval
  Future<void> clearAuthenticationForPendingApproval() async {
    developer.log('Clearing authentication data for pending approval',
        name: 'auth_provider');

    try {
      // Clear token, user data, and user state
      await _apiService.clearToken();
      await _apiService.clearUserData();
      _user = null;
      _setLoading(false);
      notifyListeners();

      developer.log('Authentication data cleared for pending approval',
          name: 'auth_provider');
    } catch (e) {
      developer.log(
          'Error clearing authentication data for pending approval: $e',
          name: 'auth_provider');
      // Still clear local state even if there's an error
      _user = null;
      _setLoading(false);
      notifyListeners();
    }
  }

  // Verify bank account
  Future<Map<String, dynamic>> verifyBankAccount({
    required String accountNumber,
    required String bankCode,
  }) async {
    developer.log('Verifying bank account', name: 'auth_provider');
    developer.log(
        'Bank verification data: accountNumber=$accountNumber, bankCode=$bankCode',
        name: 'auth_provider');

    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.get(
        ApiEndpoints.verifyBankAccount,
        queryParams: {
          'accountNumber': accountNumber,
          'bankName':
              bankCode, // API expects 'bankName' parameter but we send the bank code
        },
        requiresAuth: false,
      );

      developer.log('Bank verification response received',
          name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');
      developer.log('Response error: ${response.error}', name: 'auth_provider');

      _setLoading(false);

      if (response.success && response.data != null) {
        developer.log('Bank verification successful', name: 'auth_provider');

        // Return the response data with improved account name extraction
        String accountName = '';

        // Try to extract account name from the response data
        if (response.data['data'] != null && response.data['data'] is Map) {
          final accountNameData = response.data['data']['account_name'];
          if (accountNameData != null &&
              accountNameData.toString().isNotEmpty) {
            accountName = accountNameData.toString();
          }
        }

        developer.log('Extracted account name: $accountName',
            name: 'auth_provider');
        developer.log('Full response data: ${response.data}',
            name: 'auth_provider');

        return {
          'success': true,
          'data': response.data,
          'message': response.data['responseMsg'] ??
              'Bank account verified successfully',
          'accountName': accountName,
        };
      } else {
        // Extract the most useful error message from the response
        String errorMessage =
            response.error ?? 'Bank account verification failed';

        // Log the detailed error
        developer.log('Bank verification failed: $errorMessage',
            name: 'auth_provider');

        // Set the error in the provider state
        _setError(errorMessage);

        // Return a structured error response
        return {
          'success': false,
          'message': errorMessage,
          // Include the raw response data if available for debugging
          'data': response.data,
        };
      }
    } on TimeoutException {
      _setLoading(false);
      String timeoutMessage =
          'The server is taking too long to respond. Please try again later.';
      _setError(timeoutMessage);
      return {
        'success': false,
        'message': timeoutMessage,
      };
    } catch (e) {
      _setLoading(false);
      developer.log('Unexpected error during bank verification: $e',
          name: 'auth_provider');
      String errorMessage = 'An unexpected error occurred. Please try again.';
      _setError(errorMessage);
      return {
        'success': false,
        'message': errorMessage,
        'error': e.toString(), // Include the raw error for debugging
      };
    }
  }

  // Save banks list to cache
  Future<void> _saveBanksToCache(List<Bank> banks) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final banksJson = banks.map((bank) => bank.toJson()).toList();
      await prefs.setString('cached_banks', jsonEncode(banksJson));
      developer.log('Banks list saved to cache', name: 'auth_provider');
    } catch (e) {
      developer.log('Error saving banks to cache: $e', name: 'auth_provider');
    }
  }

  // Load banks list from cache
  Future<List<Bank>> _loadBanksFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedBanksJson = prefs.getString('cached_banks');

      if (cachedBanksJson != null) {
        final List<dynamic> banksData = jsonDecode(cachedBanksJson);
        final banks =
            banksData.map((bankData) => Bank.fromJson(bankData)).toList();

        developer.log('Loaded ${banks.length} banks from cache',
            name: 'auth_provider');
        return banks;
      }
    } catch (e) {
      developer.log('Error loading banks from cache: $e',
          name: 'auth_provider');
    }

    return [];
  }

  // Fetch banks list
  Future<Map<String, dynamic>> fetchBanks() async {
    developer.log('Fetching banks list', name: 'auth_provider');

    _setLoadingBanks(true);
    _setBanksError(null);

    // Try to load banks from cache first
    final cachedBanks = await _loadBanksFromCache();
    if (cachedBanks.isNotEmpty) {
      developer.log('Using cached banks list', name: 'auth_provider');
      _banks.clear();
      _banks.addAll(cachedBanks);
      notifyListeners();
    }

    try {
      // First try the direct API test to see if we can reach the endpoint
      developer.log('Trying direct API test first', name: 'auth_provider');
      final directTestResult = await _apiService.testBanksListApi();

      if (directTestResult['success'] == true) {
        developer.log('Direct API test successful', name: 'auth_provider');

        // Process the direct API response
        final data = directTestResult['data'];
        if (data != null &&
            data is Map &&
            data.containsKey('data') &&
            data['data'] is List) {
          developer.log('Processing direct API response data',
              name: 'auth_provider');

          // Clear existing banks
          _banks.clear();

          // Parse the banks from the response
          final banksList = data['data'] as List;
          for (var bankData in banksList) {
            if (bankData is Map<String, dynamic>) {
              _banks.add(Bank.fromJson(bankData));
            }
          }

          // Save banks to cache for future use
          if (_banks.isNotEmpty) {
            _saveBanksToCache(_banks);
          }

          _setLoadingBanks(false);
          notifyListeners();

          return {
            'success': true,
            'data': data,
            'message': data['responseMsg'] ?? 'Banks list fetched successfully',
            'directTest': true,
          };
        }
      } else {
        developer.log(
            'Direct API test failed, falling back to regular API call',
            name: 'auth_provider');
      }

      // If direct test fails or doesn't return expected data, fall back to regular API call
      final response = await _apiService.get(
        ApiEndpoints.bank,
        requiresAuth: false,
        useExtendedTimeout: true, // Use extended timeout for banks list API
        retries: 3, // Increase retries for this endpoint
      );

      developer.log('Banks list response received', name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');

      _setLoadingBanks(false);

      if (response.success && response.data != null) {
        developer.log('Banks list fetched successfully', name: 'auth_provider');

        // Clear existing banks
        _banks.clear();

        // Parse the banks from the response
        if (response.data['data'] != null && response.data['data'] is List) {
          final banksList = response.data['data'] as List;
          for (var bankData in banksList) {
            if (bankData is Map<String, dynamic>) {
              _banks.add(Bank.fromJson(bankData));
            }
          }

          // Save banks to cache for future use
          if (_banks.isNotEmpty) {
            _saveBanksToCache(_banks);
          }
        }

        notifyListeners();

        return {
          'success': true,
          'data': response.data,
          'message':
              response.data['responseMsg'] ?? 'Banks list fetched successfully',
        };
      } else {
        // Extract the most useful error message from the response
        String errorMessage = response.error ?? 'Failed to fetch banks list';

        // Log the detailed error
        developer.log('Failed to fetch banks list: $errorMessage',
            name: 'auth_provider');

        // Set the error in the provider state
        _setBanksError(errorMessage);

        // If we have cached banks, use them and return partial success
        if (cachedBanks.isNotEmpty) {
          return {
            'success': true,
            'message': 'Using cached banks list. $errorMessage',
            'usingCache': true,
          };
        }

        // Return a structured error response
        return {
          'success': false,
          'message': errorMessage,
          'data': response.data,
        };
      }
    } on TimeoutException {
      _setLoadingBanks(false);
      String timeoutMessage =
          'The server is taking too long to respond. Please try again later.';
      _setBanksError(timeoutMessage);

      // If we have cached banks, use them and return partial success
      if (cachedBanks.isNotEmpty) {
        return {
          'success': true,
          'message': 'Using cached banks list. $timeoutMessage',
          'usingCache': true,
        };
      }

      return {
        'success': false,
        'message': timeoutMessage,
      };
    } catch (e) {
      _setLoadingBanks(false);
      developer.log('Unexpected error while fetching banks list: $e',
          name: 'auth_provider');
      String errorMessage = 'An unexpected error occurred. Please try again.';
      _setBanksError(errorMessage);

      // If we have cached banks, use them and return partial success
      if (cachedBanks.isNotEmpty) {
        return {
          'success': true,
          'message': 'Using cached banks list. $errorMessage',
          'usingCache': true,
        };
      }

      return {
        'success': false,
        'message': errorMessage,
        'error': e.toString(),
      };
    }
  }

  // Save states list to cache
  Future<void> _saveStatesToCache(List<app_state.State> states) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statesJson = states.map((state) => state.toJson()).toList();
      await prefs.setString('cached_states', jsonEncode(statesJson));
      developer.log('States list saved to cache', name: 'auth_provider');
    } catch (e) {
      developer.log('Error saving states to cache: $e', name: 'auth_provider');
    }
  }

  // Load states list from cache
  Future<List<app_state.State>> _loadStatesFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedStatesJson = prefs.getString('cached_states');

      if (cachedStatesJson != null) {
        final List<dynamic> statesData = jsonDecode(cachedStatesJson);
        final states = statesData
            .map((stateName) {
              if (stateName is String) {
                return app_state.State.fromJson(stateName);
              } else if (stateName is Map<String, dynamic> &&
                  stateName.containsKey('name')) {
                return app_state.State(name: stateName['name']);
              }
              return app_state.State(name: '');
            })
            .where((state) => state.name.isNotEmpty)
            .toList();

        developer.log('Loaded ${states.length} states from cache',
            name: 'auth_provider');
        return states;
      }
    } catch (e) {
      developer.log('Error loading states from cache: $e',
          name: 'auth_provider');
    }

    return [];
  }

  // Fetch states list
  Future<Map<String, dynamic>> fetchStates() async {
    developer.log('Fetching states list', name: 'auth_provider');

    _setLoadingStates(true);
    _setStatesError(null);

    // Try to load states from cache first
    final cachedStates = await _loadStatesFromCache();
    if (cachedStates.isNotEmpty) {
      developer.log('Using cached states list', name: 'auth_provider');
      _states.clear();
      _states.addAll(cachedStates);
      notifyListeners();
    }

    try {
      final response = await _apiService.get(
        ApiEndpoints.states,
        requiresAuth: false,
        useExtendedTimeout: true, // Use extended timeout for states list API
        retries: 3, // Increase retries for this endpoint
      );

      developer.log('States list response received', name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');

      _setLoadingStates(false);

      if (response.success && response.data != null) {
        developer.log('States list fetched successfully',
            name: 'auth_provider');

        // Clear existing states
        _states.clear();

        // Parse the states from the response
        if (response.data['data'] != null && response.data['data'] is List) {
          final statesList = response.data['data'] as List;
          for (var stateName in statesList) {
            if (stateName is String) {
              _states.add(app_state.State(name: stateName));
            }
          }

          // Save states to cache for future use
          if (_states.isNotEmpty) {
            _saveStatesToCache(_states);
          }
        }

        notifyListeners();

        return {
          'success': true,
          'data': response.data,
          'message': response.data['responseMsg'] ??
              'States list fetched successfully',
        };
      } else {
        // Extract the most useful error message from the response
        String errorMessage = response.error ?? 'Failed to fetch states list';

        // Log the detailed error
        developer.log('Failed to fetch states list: $errorMessage',
            name: 'auth_provider');

        // Set the error in the provider state
        _setStatesError(errorMessage);

        // If we have cached states, use them and return partial success
        if (cachedStates.isNotEmpty) {
          return {
            'success': true,
            'message': 'Using cached states list. $errorMessage',
            'usingCache': true,
          };
        }

        // Return a structured error response
        return {
          'success': false,
          'message': errorMessage,
          'data': response.data,
        };
      }
    } on TimeoutException {
      _setLoadingStates(false);
      String timeoutMessage =
          'The server is taking too long to respond. Please try again later.';
      _setStatesError(timeoutMessage);

      // If we have cached states, use them and return partial success
      if (cachedStates.isNotEmpty) {
        return {
          'success': true,
          'message': 'Using cached states list. $timeoutMessage',
          'usingCache': true,
        };
      }

      return {
        'success': false,
        'message': timeoutMessage,
      };
    } catch (e) {
      _setLoadingStates(false);
      developer.log('Unexpected error while fetching states list: $e',
          name: 'auth_provider');
      String errorMessage = 'An unexpected error occurred. Please try again.';
      _setStatesError(errorMessage);

      // If we have cached states, use them and return partial success
      if (cachedStates.isNotEmpty) {
        return {
          'success': true,
          'message': 'Using cached states list. $errorMessage',
          'usingCache': true,
        };
      }

      return {
        'success': false,
        'message': errorMessage,
        'error': e.toString(),
      };
    }
  }

  // Request password reset (sends email with reset token)
  Future<Map<String, dynamic>> forgotPassword({
    required String email,
  }) async {
    developer.log('Requesting password reset', name: 'auth_provider');
    developer.log('Forgot password data: email=$email', name: 'auth_provider');

    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.post(
        ApiEndpoints.forgotPassword,
        body: {
          'email': email,
        },
        requiresAuth: false,
      );

      developer.log('Forgot password response received', name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');
      developer.log('Response error: ${response.error}', name: 'auth_provider');

      _setLoading(false);

      if (response.success && response.data != null) {
        developer.log('Password reset request successful',
            name: 'auth_provider');

        // Check response code to confirm success
        final responseCode = response.data['responseCode'];
        if (responseCode == '00') {
          return {
            'success': true,
            'message': response.data['responseMsg'] ??
                'Reset instructions sent to your email',
          };
        } else {
          // API returned a non-success response code
          String errorMessage = response.data['responseMsg'] ??
              'Failed to send reset instructions';
          developer.log('Password reset request failed: $errorMessage',
              name: 'auth_provider');
          _setError(errorMessage);
          return {
            'success': false,
            'message': errorMessage,
          };
        }
      } else {
        // Extract the most useful error message from the response
        String errorMessage =
            response.error ?? 'Failed to send reset instructions';
        developer.log('Password reset request failed: $errorMessage',
            name: 'auth_provider');
        _setError(errorMessage);
        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } on TimeoutException {
      _setLoading(false);
      String timeoutMessage =
          'The server is taking too long to respond. Please try again later.';
      _setError(timeoutMessage);
      return {
        'success': false,
        'message': timeoutMessage,
      };
    } catch (e) {
      developer.log('Unexpected error during password reset request: $e',
          name: 'auth_provider');
      _setLoading(false);
      _setError('An unexpected error occurred. Please try again.');
      return {
        'success': false,
        'message': 'An unexpected error occurred. Please try again.',
      };
    }
  }

  // Verify OTP for forgot password
  Future<Map<String, dynamic>> verifyOtpForForgotPassword({
    required String email,
    required String otp,
  }) async {
    developer.log('Verifying OTP for forgot password', name: 'auth_provider');
    developer.log('Verification data: email=$email, otp=$otp',
        name: 'auth_provider');

    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.post(
        ApiEndpoints.verifyOtpForForgotPassword,
        body: {
          'email': email,
          'otp': otp,
        },
        requiresAuth: false,
      );

      developer.log('OTP verification response received',
          name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');
      developer.log('Response error: ${response.error}', name: 'auth_provider');

      _setLoading(false);

      if (response.success && response.data != null) {
        developer.log('OTP verification successful', name: 'auth_provider');

        // Check response code to confirm success
        final responseCode = response.data['responseCode'];
        if (responseCode == '00') {
          // Extract token from response if available
          String? token;
          if (response.data['data'] != null && response.data['data'] is Map) {
            token = response.data['data']['token']?.toString();
          }

          return {
            'success': true,
            'message':
                response.data['responseMsg'] ?? 'OTP verification successful',
            'token': token,
          };
        } else {
          // API returned a non-success response code
          String errorMessage =
              response.data['responseMsg'] ?? 'OTP verification failed';
          developer.log('OTP verification failed: $errorMessage',
              name: 'auth_provider');
          _setError(errorMessage);
          return {
            'success': false,
            'message': errorMessage,
          };
        }
      } else {
        // Extract the most useful error message from the response
        String errorMessage = response.error ?? 'OTP verification failed';
        developer.log('OTP verification failed: $errorMessage',
            name: 'auth_provider');
        _setError(errorMessage);
        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } on TimeoutException {
      _setLoading(false);
      String timeoutMessage =
          'The server is taking too long to respond. Please try again later.';
      _setError(timeoutMessage);
      return {
        'success': false,
        'message': timeoutMessage,
      };
    } catch (e) {
      developer.log('Unexpected error during OTP verification: $e',
          name: 'auth_provider');
      _setLoading(false);
      _setError('An unexpected error occurred. Please try again.');
      return {
        'success': false,
        'message': 'An unexpected error occurred. Please try again.',
      };
    }
  }

  // Reset password
  Future<Map<String, dynamic>> resetPassword({
    required String token,
    required String newPassword,
    String? email,
  }) async {
    developer.log('Starting password reset', name: 'auth_provider');
    developer.log(
        'Reset password data: token provided=${token.isNotEmpty}, email=${email ?? "not provided"}',
        name: 'auth_provider');

    _setLoading(true);
    _setError(null);

    try {
      // Using POST with JSON body for reset password
      // According to the API documentation, reset-password endpoint only accepts
      // "email" and "newPassword" fields, not "otp"
      // Prepare the request body
      final Map<String, dynamic> requestBody = {
        'newPassword': newPassword,
      };

      // Email is required for the reset password API
      if (email != null && email.isNotEmpty) {
        requestBody['email'] = email;
      } else {
        // If email is not provided, we can't proceed with the reset
        _setLoading(false);
        _setError('Email is required for password reset');
        return {
          'success': false,
          'message': 'Email is required for password reset',
        };
      }

      final response = await _apiService.post(
        ApiEndpoints.resetPassword,
        body: requestBody,
        requiresAuth: false,
      );

      developer.log('Reset password response received', name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');
      developer.log('Response error: ${response.error}', name: 'auth_provider');

      _setLoading(false);

      if (response.success && response.data != null) {
        developer.log('Password reset successful', name: 'auth_provider');

        // Check response code to confirm success
        final responseCode = response.data['responseCode'];
        if (responseCode == '00') {
          return {
            'success': true,
            'message':
                response.data['responseMsg'] ?? 'Password reset successful',
          };
        } else {
          // API returned a non-success response code
          String errorMessage =
              response.data['responseMsg'] ?? 'Password reset failed';
          developer.log('Password reset failed: $errorMessage',
              name: 'auth_provider');
          _setError(errorMessage);
          return {
            'success': false,
            'message': errorMessage,
          };
        }
      } else {
        // Extract the most useful error message from the response
        String errorMessage = response.error ?? 'Password reset failed';
        developer.log('Password reset failed: $errorMessage',
            name: 'auth_provider');
        _setError(errorMessage);
        return {
          'success': false,
          'message': errorMessage,
        };
      }
    } on TimeoutException {
      _setLoading(false);
      String timeoutMessage =
          'The server is taking too long to respond. Please try again later.';
      _setError(timeoutMessage);
      return {
        'success': false,
        'message': timeoutMessage,
      };
    } catch (e) {
      developer.log('Unexpected error during password reset: $e',
          name: 'auth_provider');
      _setLoading(false);
      _setError('An unexpected error occurred. Please try again.');
      return {
        'success': false,
        'message': 'An unexpected error occurred. Please try again.',
      };
    }
  }

  // Register a new user
  Future<Map<String, dynamic>> signup({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String phone,
    required String accountNumber,
    required String accountName,
    required String bankName,
    required String state,
  }) async {
    developer.log('Starting user registration', name: 'auth_provider');
    developer.log(
        'Registration data: email=$email, firstName=$firstName, lastName=$lastName',
        name: 'auth_provider');

    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.post(
        ApiEndpoints.register,
        body: {
          'first_name': firstName,
          'last_name': lastName,
          'email': email,
          'password': password,
          'phone': phone,
          'accountNumber': accountNumber,
          'accountName': accountName,
          'bankName': bankName,
          'state': state,
        },
        requiresAuth: false,
      );

      developer.log('Registration response received', name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');
      developer.log('Response error: ${response.error}', name: 'auth_provider');

      _setLoading(false);

      if (response.success && response.data != null) {
        developer.log('Registration successful', name: 'auth_provider');

        // Return the response data for further processing
        // This might include OTP verification or other steps
        return {
          'success': true,
          'data': response.data,
          'message': response.data['responseMsg'] ?? 'Registration successful',
        };
      } else {
        // Extract the most useful error message from the response
        String errorMessage = response.error ?? 'Registration failed';

        // Log the detailed error
        developer.log('Registration failed: $errorMessage',
            name: 'auth_provider');

        // Set the error in the provider state
        _setError(errorMessage);

        // Return a structured error response
        return {
          'success': false,
          'message': errorMessage,
          // Include the raw response data if available for debugging
          'data': response.data,
        };
      }
    } on TimeoutException {
      _setLoading(false);
      String timeoutMessage =
          'The server is taking too long to respond. Please try again later.';
      _setError(timeoutMessage);
      return {
        'success': false,
        'message': timeoutMessage,
      };
    } catch (e) {
      _setLoading(false);
      developer.log('Unexpected error during registration: $e',
          name: 'auth_provider');
      String errorMessage = 'An unexpected error occurred. Please try again.';
      _setError(errorMessage);
      return {
        'success': false,
        'message': errorMessage,
        'error': e.toString(), // Include the raw error for debugging
      };
    }
  }

  // Fetch DVA information for the authenticated user
  Future<bool> fetchDvaInfo() async {
    developer.log('Fetching DVA information', name: 'auth_provider');

    if (_user?.email == null) {
      developer.log('No user email available for DVA fetch',
          name: 'auth_provider');
      _setDvaError('User not authenticated');
      return false;
    }

    _setLoadingDva(true);
    _setDvaError(null);

    try {
      final response = await _apiService.get(
        ApiEndpoints.dvaInfo(_user!.email!),
        requiresAuth: true,
      );

      developer.log('DVA info response received', name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');
      developer.log('Response error: ${response.error}', name: 'auth_provider');

      _setLoadingDva(false);

      if (response.success && response.data != null) {
        developer.log('DVA info fetch successful', name: 'auth_provider');

        // Check if response has standard format with responseCode
        if (response.data['responseCode'] == '00' &&
            response.data['data'] != null) {
          try {
            _dvaInfo = DvaInfo.fromJson(response.data['data']);
            developer.log('DVA info parsed successfully: $_dvaInfo',
                name: 'auth_provider');
            notifyListeners();
            return true;
          } catch (e) {
            developer.log('Error parsing DVA info: $e', name: 'auth_provider');
            _setDvaError('Failed to parse DVA information');
            return false;
          }
        }
        // Check if response contains direct DVA info fields (no wrapper)
        else if (response.data['accountName'] != null) {
          try {
            _dvaInfo = DvaInfo.fromJson(response.data);
            developer.log(
                'DVA info parsed successfully from direct data: $_dvaInfo',
                name: 'auth_provider');
            notifyListeners();
            return true;
          } catch (e) {
            developer.log('Error parsing direct DVA info: $e',
                name: 'auth_provider');
            _setDvaError('Failed to parse DVA information');
            return false;
          }
        } else {
          // API returned error in standard format
          String errorMessage =
              response.data['responseMsg'] ?? 'Failed to fetch DVA information';
          developer.log('DVA info fetch failed: $errorMessage',
              name: 'auth_provider');
          _setDvaError(errorMessage);
          return false;
        }
      } else {
        // API returned an error
        String errorMessage =
            response.error ?? 'Failed to fetch DVA information';
        developer.log('DVA info fetch failed: $errorMessage',
            name: 'auth_provider');
        _setDvaError(errorMessage);
        return false;
      }
    } catch (e) {
      _setLoadingDva(false);
      developer.log('Unexpected error during DVA info fetch: $e',
          name: 'auth_provider');
      String errorMessage = 'An unexpected error occurred. Please try again.';
      _setDvaError(errorMessage);
      return false;
    }
  }

  // Fetch delivery summary for the authenticated user
  Future<bool> fetchDeliverySummary() async {
    developer.log('Fetching delivery summary', name: 'auth_provider');

    if (_user?.email == null) {
      developer.log('No user email available for delivery summary fetch',
          name: 'auth_provider');
      _setDeliverySummaryError('User not authenticated');
      return false;
    }

    _setLoadingDeliverySummary(true);
    _setDeliverySummaryError(null);

    try {
      final response = await _apiService.get(
        ApiEndpoints.deliverySummary,
        requiresAuth: true,
      );

      developer.log('Delivery summary response received',
          name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');
      developer.log('Response error: ${response.error}', name: 'auth_provider');

      _setLoadingDeliverySummary(false);

      if (response.success && response.data != null) {
        developer.log('Delivery summary fetch successful',
            name: 'auth_provider');

        // Check if response has standard format with responseCode
        if (response.data['responseCode'] == '00' &&
            response.data['data'] != null) {
          try {
            _deliverySummary = DeliverySummary.fromJson(response.data['data']);
            developer.log(
                'Delivery summary parsed successfully: $_deliverySummary',
                name: 'auth_provider');
            notifyListeners();
            return true;
          } catch (e) {
            developer.log('Error parsing delivery summary: $e',
                name: 'auth_provider');
            _setDeliverySummaryError('Failed to parse delivery summary');
            return false;
          }
        }
        // Check if response contains direct delivery summary fields (no wrapper)
        else if (response.data['totalDeliveries'] != null ||
            response.data['deliveredCount'] != null ||
            response.data['pendingCount'] != null) {
          try {
            _deliverySummary = DeliverySummary.fromJson(response.data);
            developer.log(
                'Delivery summary parsed successfully from direct data: $_deliverySummary',
                name: 'auth_provider');
            notifyListeners();
            return true;
          } catch (e) {
            developer.log('Error parsing direct delivery summary: $e',
                name: 'auth_provider');
            _setDeliverySummaryError('Failed to parse delivery summary');
            return false;
          }
        } else {
          // API returned error in standard format
          String errorMessage = response.data['responseMsg'] ??
              'Failed to fetch delivery summary';
          developer.log('Delivery summary fetch failed: $errorMessage',
              name: 'auth_provider');
          _setDeliverySummaryError(errorMessage);
          return false;
        }
      } else {
        // API returned an error
        String errorMessage =
            response.error ?? 'Failed to fetch delivery summary';
        developer.log('Delivery summary fetch failed: $errorMessage',
            name: 'auth_provider');
        _setDeliverySummaryError(errorMessage);
        return false;
      }
    } catch (e) {
      _setLoadingDeliverySummary(false);
      developer.log('Unexpected error during delivery summary fetch: $e',
          name: 'auth_provider');
      String errorMessage = 'An unexpected error occurred. Please try again.';
      _setDeliverySummaryError(errorMessage);
      return false;
    }
  }

  // Fetch deliveries list for the authenticated user
  Future<bool> fetchDeliveries() async {
    developer.log('Fetching deliveries list', name: 'auth_provider');

    if (_user?.email == null) {
      developer.log('No user email available for deliveries fetch',
          name: 'auth_provider');
      _setDeliveriesError('User not authenticated');
      return false;
    }

    _setLoadingDeliveries(true);
    _setDeliveriesError(null);

    try {
      final response = await _apiService.get(
        ApiEndpoints.deliveries,
        requiresAuth: true,
      );

      developer.log('Deliveries response received', name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');
      developer.log('Response error: ${response.error}', name: 'auth_provider');

      _setLoadingDeliveries(false);

      if (response.success && response.data != null) {
        developer.log('Deliveries fetch successful', name: 'auth_provider');

        // Check if response has standard format with responseCode
        if (response.data['responseCode'] == '00' &&
            response.data['data'] != null) {
          try {
            final deliveriesData = response.data['data'] as List;
            _deliveries.clear();
            _deliveries.addAll(deliveriesData
                .map((deliveryJson) => Delivery.fromJson(deliveryJson))
                .toList());
            developer.log(
                'Deliveries parsed successfully: ${_deliveries.length} items',
                name: 'auth_provider');
            notifyListeners();
            return true;
          } catch (e) {
            developer.log('Error parsing deliveries: $e',
                name: 'auth_provider');
            _setDeliveriesError('Failed to parse deliveries data');
            return false;
          }
        }
        // Check if response contains direct deliveries array (no wrapper)
        else if (response.data is List) {
          try {
            final deliveriesData = response.data as List;
            _deliveries.clear();
            _deliveries.addAll(deliveriesData
                .map((deliveryJson) => Delivery.fromJson(deliveryJson))
                .toList());
            developer.log(
                'Deliveries parsed successfully from direct data: ${_deliveries.length} items',
                name: 'auth_provider');
            notifyListeners();
            return true;
          } catch (e) {
            developer.log('Error parsing direct deliveries: $e',
                name: 'auth_provider');
            _setDeliveriesError('Failed to parse deliveries data');
            return false;
          }
        } else {
          // API returned error in standard format
          String errorMessage =
              response.data['responseMsg'] ?? 'Failed to fetch deliveries';
          developer.log('Deliveries fetch failed: $errorMessage',
              name: 'auth_provider');
          _setDeliveriesError(errorMessage);
          return false;
        }
      } else {
        // API returned an error
        String errorMessage = response.error ?? 'Failed to fetch deliveries';
        developer.log('Deliveries fetch failed: $errorMessage',
            name: 'auth_provider');
        _setDeliveriesError(errorMessage);
        return false;
      }
    } catch (e) {
      _setLoadingDeliveries(false);
      developer.log('Unexpected error during deliveries fetch: $e',
          name: 'auth_provider');
      String errorMessage = 'An unexpected error occurred. Please try again.';
      _setDeliveriesError(errorMessage);
      return false;
    }
  }

  // Mark delivery as delivered
  Future<bool> markDeliveryAsDelivered(String deliveryCode) async {
    developer.log('Marking delivery as delivered: $deliveryCode',
        name: 'auth_provider');

    if (_user?.email == null) {
      developer.log('No user email available for mark delivery',
          name: 'auth_provider');
      return false;
    }

    try {
      final response = await _apiService.put(
        ApiEndpoints.markDelivered(deliveryCode),
        requiresAuth: true,
      );

      developer.log('Mark delivery response: ${response.success}',
          name: 'auth_provider');
      developer.log('Mark delivery data: ${response.data}',
          name: 'auth_provider');

      if (response.success && response.data != null) {
        // Check if response has standard format with responseCode
        if (response.data['responseCode'] == '00') {
          developer.log('Delivery marked as delivered successfully',
              name: 'auth_provider');

          // Update the local delivery in the list
          final deliveryIndex =
              _deliveries.indexWhere((d) => d.deliveryCode == deliveryCode);
          if (deliveryIndex != -1) {
            // Create a new delivery object with updated status
            final updatedDelivery = Delivery(
              id: _deliveries[deliveryIndex].id,
              productName: _deliveries[deliveryIndex].productName,
              qty: _deliveries[deliveryIndex].qty,
              productPrice: _deliveries[deliveryIndex].productPrice,
              deliveryFee: _deliveries[deliveryIndex].deliveryFee,
              receiverPhone: _deliveries[deliveryIndex].receiverPhone,
              receiverName: _deliveries[deliveryIndex].receiverName,
              receiverAddress: _deliveries[deliveryIndex].receiverAddress,
              deliveryCode: _deliveries[deliveryIndex].deliveryCode,
              deliveryStatus: 'DELIVERED',
              paymentStatus: _deliveries[deliveryIndex].paymentStatus,
              dueDate: _deliveries[deliveryIndex].dueDate,
              uploadDate: _deliveries[deliveryIndex].uploadDate,
            );

            _deliveries[deliveryIndex] = updatedDelivery;
            notifyListeners();
          }

          // Refresh delivery summary to reflect the change
          fetchDeliverySummary();

          return true;
        } else {
          // API returned error in standard format
          String errorMessage = response.data['responseMsg'] ??
              'Failed to mark delivery as delivered';
          developer.log('Mark delivery failed: $errorMessage',
              name: 'auth_provider');
          return false;
        }
      } else {
        // API returned an error
        String errorMessage =
            response.error ?? 'Failed to mark delivery as delivered';
        developer.log('Mark delivery failed: $errorMessage',
            name: 'auth_provider');
        return false;
      }
    } catch (e) {
      developer.log('Unexpected error during mark delivery: $e',
          name: 'auth_provider');
      return false;
    }
  }

  // Fetch notifications for the current user
  Future<bool> fetchNotifications() async {
    if (_user == null) {
      developer.log('No user available for notifications fetch',
          name: 'auth_provider');
      _setNotificationsError('User not authenticated');
      return false;
    }

    developer.log('Fetching notifications for user: ${_user!.userId}',
        name: 'auth_provider');

    _setLoadingNotifications(true);
    _setNotificationsError(null);

    try {
      final response = await _apiService.get(
        ApiEndpoints.notifications(_user!.userId),
        requiresAuth: true,
      );

      developer.log('Notifications response received', name: 'auth_provider');
      developer.log('Response success: ${response.success}',
          name: 'auth_provider');
      developer.log('Response data: ${response.data}', name: 'auth_provider');
      developer.log('Response error: ${response.error}', name: 'auth_provider');

      _setLoadingNotifications(false);

      if (response.success && response.data != null) {
        developer.log('Notifications fetch successful', name: 'auth_provider');

        // Check if response has standard format with responseCode
        if (response.data is Map<String, dynamic> &&
            response.data['responseCode'] == '00' &&
            response.data['data'] != null) {
          try {
            final notificationsData = response.data['data'] as List;
            _notifications.clear();
            _notifications.addAll(notificationsData
                .map((notificationJson) =>
                    NotificationModel.fromJson(notificationJson))
                .toList());

            // Sort notifications by date (newest first)
            _notifications.sort((a, b) => b.dateTime.compareTo(a.dateTime));

            developer.log(
                'Notifications parsed successfully from standard format: ${_notifications.length} items',
                name: 'auth_provider');
            notifyListeners();
            return true;
          } catch (e) {
            developer.log('Error parsing standard format notifications: $e',
                name: 'auth_provider');
            _setNotificationsError('Failed to parse notifications data');
            return false;
          }
        }
        // Check if response contains direct notifications array (no wrapper)
        else if (response.data is List) {
          try {
            final notificationsData = response.data as List;
            _notifications.clear();
            _notifications.addAll(notificationsData
                .map((notificationJson) =>
                    NotificationModel.fromJson(notificationJson))
                .toList());

            // Sort notifications by date (newest first)
            _notifications.sort((a, b) => b.dateTime.compareTo(a.dateTime));

            developer.log(
                'Notifications parsed successfully from direct array: ${_notifications.length} items',
                name: 'auth_provider');
            notifyListeners();
            return true;
          } catch (e) {
            developer.log('Error parsing direct array notifications: $e',
                name: 'auth_provider');
            _setNotificationsError('Failed to parse notifications data');
            return false;
          }
        } else {
          // Response doesn't have expected structure or responseCode is not '00'
          String errorMessage = 'Failed to fetch notifications';
          if (response.data is Map<String, dynamic>) {
            errorMessage = response.data['responseMsg'] ?? errorMessage;
          }
          developer.log('Notifications fetch failed: $errorMessage',
              name: 'auth_provider');
          _setNotificationsError(errorMessage);
          return false;
        }
      } else {
        // API returned an error
        String errorMessage = response.error ?? 'Failed to fetch notifications';
        developer.log('Notifications fetch failed: $errorMessage',
            name: 'auth_provider');
        _setNotificationsError(errorMessage);
        return false;
      }
    } catch (e) {
      _setLoadingNotifications(false);
      developer.log('Unexpected error during notifications fetch: $e',
          name: 'auth_provider');
      String errorMessage = 'An unexpected error occurred. Please try again.';
      _setNotificationsError(errorMessage);
      return false;
    }
  }

  // Mark a notification as read
  Future<bool> markNotificationAsRead(int notificationId) async {
    if (_user == null) {
      developer.log('No user available for marking notification as read',
          name: 'auth_provider');
      return false;
    }

    developer.log('Marking notification as read: $notificationId',
        name: 'auth_provider');

    try {
      // Find the notification in the local list first
      final notificationIndex =
          _notifications.indexWhere((n) => n.id == notificationId);

      if (notificationIndex == -1) {
        developer.log('Notification not found in local list',
            name: 'auth_provider');
        return false;
      }

      // Make API call to mark notification as read
      final response = await _apiService.post(
        ApiEndpoints.markNotificationAsRead(notificationId),
        requiresAuth: true,
      );

      if (response.success && response.data != null) {
        developer.log('Mark notification as read API call successful',
            name: 'auth_provider');

        // Check if response has standard format with responseCode
        if (response.data['responseCode'] == '00') {
          // Update the notification locally only after successful API response
          final updatedNotification =
              _notifications[notificationIndex].copyWith(read: true);
          _notifications[notificationIndex] = updatedNotification;
          notifyListeners();

          developer.log('Notification marked as read successfully',
              name: 'auth_provider');
          return true;
        } else {
          // API returned error response
          String errorMessage = response.data['responseMsg'] ??
              'Failed to mark notification as read';
          developer.log('Mark notification as read failed: $errorMessage',
              name: 'auth_provider');
          return false;
        }
      } else {
        // API returned an error
        String errorMessage =
            response.error ?? 'Failed to mark notification as read';
        developer.log('Mark notification as read API failed: $errorMessage',
            name: 'auth_provider');
        return false;
      }
    } catch (e) {
      developer.log('Error marking notification as read: $e',
          name: 'auth_provider');
      return false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllNotificationsAsRead() async {
    developer.log('Marking all notifications as read', name: 'auth_provider');

    try {
      // Update all notifications locally
      for (int i = 0; i < _notifications.length; i++) {
        if (!_notifications[i].read) {
          _notifications[i] = _notifications[i].copyWith(read: true);
        }
      }

      notifyListeners();
      developer.log('All notifications marked as read locally',
          name: 'auth_provider');
      return true;
    } catch (e) {
      developer.log('Error marking all notifications as read: $e',
          name: 'auth_provider');
      return false;
    }
  }

  // Get unread notifications count
  int get unreadNotificationsCount {
    return _notifications.where((notification) => !notification.read).length;
  }

  // Filter notifications by search query
  List<NotificationModel> filterNotifications(String query) {
    if (query.isEmpty) {
      return _notifications;
    }

    return _notifications.where((notification) {
      return notification.message.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // Group notifications by date
  Map<String, List<NotificationModel>> get groupedNotifications {
    final Map<String, List<NotificationModel>> grouped = {};

    for (final notification in _notifications) {
      final dateKey = notification.formattedDate;
      if (!grouped.containsKey(dateKey)) {
        grouped[dateKey] = [];
      }
      grouped[dateKey]!.add(notification);
    }

    return grouped;
  }

  // Helper method for testing
  void setUserForTesting(User user) {
    _user = user;
  }
}
