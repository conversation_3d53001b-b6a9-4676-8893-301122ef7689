// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mialamobile/dashboard/home/<USER>';
import 'package:mialamobile/dashboard/services/service_tab.dart';
import 'package:mialamobile/dashboard/history/transaction_history.dart';
import 'package:mialamobile/dashboard/profile/profile_tab.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  // Method to programmatically switch to a specific tab
  void _switchToTab(int index) {
    _onItemTapped(index);
  }

  // Pages to display when bottom navigation items are tapped
  List<Widget> get _pages => [
        HomeTab(onNavigateToServices: () => _switchToTab(1)),
        const ServicesTab(),
        const TransactionHistory(),
        const ProfileTab(),
      ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff121212),
      body: _pages[_selectedIndex],
      bottomNavigationBar: Container(
        decoration: const BoxDecoration(
          color: Color(0xff1E1E1E),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 8,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Container(
            height: 70,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                // Home icon
                _buildNavItem(
                  iconPath: 'assets/icons/home.svg',
                  index: 0,
                  label: 'Home',
                ),
                // Package icon
                _buildNavItem(
                  iconPath: 'assets/icons/package.svg',
                  index: 1,
                  label: 'Services',
                ),
                // Receipt icon
                _buildNavItem(
                  iconPath: 'assets/icons/receipt.svg',
                  index: 2,
                  label: 'History',
                ),
                // Profile icon
                _buildNavItem(
                  iconPath: 'assets/icons/profile.svg',
                  index: 3,
                  label: 'Profile',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required String iconPath,
    required int index,
    required String label,
  }) {
    final isSelected = _selectedIndex == index;
    return GestureDetector(
      onTap: () => _onItemTapped(index),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: isSelected
              ? const Color(0xffB10303).withOpacity(0.1)
              : Colors.transparent,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              iconPath,
              color: isSelected
                  ? const Color(0xffB10303)
                  : const Color(0xff8C8C8C),
              width: 22,
              height: 22,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                color: isSelected
                    ? const Color(0xffB10303)
                    : const Color(0xff8C8C8C),
                fontSize: 9,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                height: 1.0, // Tight line height to reduce text space
              ),
            ),
          ],
        ),
      ),
    );
  }
}
