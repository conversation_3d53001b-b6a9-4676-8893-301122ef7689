import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';

class RecentDelivery extends StatelessWidget {
  final String productName;
  final String trackingId;
  final String deliveryAddress;
  final String deliveryDate;
  final String deliveryTime;
  final String deliveryStatus;
  final Color statusColor;
  final VoidCallback? onMarkAsDelivered;

  const RecentDelivery({
    super.key,
    required this.productName,
    required this.trackingId,
    required this.deliveryAddress,
    required this.deliveryDate,
    required this.deliveryTime,
    required this.deliveryStatus,
    this.statusColor = const Color(0xffB10303),
    this.onMarkAsDelivered,
  });

  void _showMarkAsDeliveredDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xff1E1E1E),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Mark as Delivered',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          content: Text(
            'Are you sure you want to mark this delivery as delivered?',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: const Color(0xff8C8C8C),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xff8C8C8C),
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onMarkAsDelivered?.call();
              },
              child: Text(
                'Mark as Delivered',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xffB10303),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if delivery is not delivered and can be marked as delivered
    final bool canMarkAsDelivered =
        deliveryStatus.toUpperCase() != 'DELIVERED' &&
            onMarkAsDelivered != null;

    return GestureDetector(
      onTap:
          canMarkAsDelivered ? () => _showMarkAsDeliveredDialog(context) : null,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xff1E1E1E),
          borderRadius: BorderRadius.circular(12),
          // Add subtle border for tappable items
          border: canMarkAsDelivered
              ? Border.all(color: const Color(0xff2A2A2A), width: 1)
              : null,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left side - Delivery information
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product delivered text
                  Text(
                    'Product Delivered',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w400,
                      color: const Color(0xff8C8C8C),
                    ),
                  ),
                  const Gap(4),

                  // Product name
                  Text(
                    productName,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const Gap(4),
                  // Tracking ID
                  Text(
                    trackingId,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const Gap(8),
                  // Address with location icon
                  Row(
                    children: [
                      SvgPicture.asset(
                        'assets/icons/location.svg',
                      ),
                      const Gap(4),
                      Expanded(
                        child: Text(
                          deliveryAddress,
                          style: GoogleFonts.poppins(
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xff8C8C8C),
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Right side - Date, time and status
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Date
                Text(
                  deliveryDate,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),

                // Time
                Text(
                  deliveryTime,
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xff8C8C8C),
                  ),
                ),

                const Gap(16),

                // Status button
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    deliveryStatus,
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
