import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/input_type.dart';
import 'package:mialamobile/components/primary_button.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:provider/provider.dart';

class ResetPassword extends StatefulWidget {
  final String? resetToken;
  final String? email;

  const ResetPassword({
    super.key,
    this.resetToken,
    this.email,
  });

  @override
  State<ResetPassword> createState() => _ResetPasswordState();
}

class _ResetPasswordState extends State<ResetPassword> {
  // Controllers for the text fields
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  // OTP controllers
  final List<TextEditingController> _otpControllers =
      List.generate(4, (_) => TextEditingController());

  // Track which stage of the password reset flow we're on
  // If reset token is provided, start at password reset stage (2)
  // Otherwise, start at email verification (0)
  late int _currentStage;

  // For password visibility
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  // For loading state

  @override
  void initState() {
    super.initState();

    // If reset token is provided, skip to password reset stage
    _currentStage = widget.resetToken != null ? 2 : 0;

    // If email is provided, set it in the email controller
    if (widget.email != null && widget.email!.isNotEmpty) {
      _emailController.text = widget.email!;
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        iconTheme: const IconThemeData(color: Color(0xff8C8C8C)),
      ),
      backgroundColor: const Color(0xff121212),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32),
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              Text(
                _getPageTitle(),
                style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w500,
                    fontSize: 24,
                    color: Colors.white),
              ),
              const Gap(65),
              // Show different content based on the current stage
              _buildCurrentStageWidget(),
            ]),
          ),
        ),
      ),
    );
  }

  // Get the title based on current stage
  String _getPageTitle() {
    switch (_currentStage) {
      case 0:
        return 'Reset Password';
      case 1:
        return 'OTP Verification';
      case 2:
        return 'Reset Password';
      default:
        return 'Reset Password';
    }
  }

  // Build the appropriate widget based on current stage
  Widget _buildCurrentStageWidget() {
    switch (_currentStage) {
      case 0:
        return _buildEmailVerificationForm();
      case 1:
        return _buildOtpVerificationForm();
      case 2:
        return _buildResetPasswordForm();
      default:
        return _buildEmailVerificationForm();
    }
  }

  // First stage: Email verification form
  Widget _buildEmailVerificationForm() {
    return Column(
      children: [
        InputType(
          labelText: 'Email',
          hintText: '<EMAIL>',
          keyboardType: TextInputType.emailAddress,
          controller: _emailController,
        ),
        const Gap(24),
        PrimaryButton(
          text: 'Verify Email',
          onPressed: () {
            // In a real app, you would verify the email with your backend
            // For this example, we'll just move to the next stage
            setState(() {
              _currentStage = 1;
            });
          },
          width: MediaQuery.of(context).size.width,
          backgroundColor: const Color(0xffFF0000),
        ),
      ],
    );
  }

  // Second stage: OTP verification form
  Widget _buildOtpVerificationForm() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(4, (index) {
            return SizedBox(
              width: 50,
              child: TextField(
                controller: _otpControllers[index],
                decoration: InputDecoration(
                  filled: true,
                  fillColor: const Color(0xff1E1E1E),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                style: const TextStyle(
                  color: Color(0xffB10303),
                  fontSize: 30,
                ),
                textAlign: TextAlign.center,
                keyboardType: TextInputType.number,
                maxLength: 1,
                buildCounter: (context,
                        {required currentLength,
                        required isFocused,
                        maxLength}) =>
                    null,
                onChanged: (value) {
                  // Auto-focus to next field
                  if (value.isNotEmpty && index < 3) {
                    FocusScope.of(context).nextFocus();
                  }
                },
              ),
            );
          }),
        ),
        const Gap(10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Didn\'t receive code?',
              style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w300,
                  fontSize: 10,
                  color: const Color(0xff8C8C8C)),
            ),
            const Gap(4),
            InkWell(
              onTap: () {
                // Resend OTP logic would go here
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Resending OTP...')),
                );
              },
              child: Text(
                'Resend',
                style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w400,
                    fontSize: 10,
                    color: const Color(0xffFF0000)),
              ),
            ),
          ],
        ),
        const Gap(24),
        PrimaryButton(
          text: 'Verify OTP',
          onPressed: () {
            // Validate OTP
            bool isOtpComplete = _otpControllers
                .every((controller) => controller.text.isNotEmpty);

            if (!isOtpComplete) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Please enter the complete OTP')),
              );
              return;
            }

            // In a real app, you would verify the OTP with your backend
            // For this example, we'll just move to the next stage
            setState(() {
              _currentStage = 2;
            });
          },
          width: MediaQuery.of(context).size.width,
          backgroundColor: const Color(0xffFF0000),
        ),
      ],
    );
  }

  // Third stage: Reset password form
  Widget _buildResetPasswordForm() {
    final authProvider = Provider.of<AuthProvider>(context, listen: true);

    return Column(
      children: [
        InputType(
          labelText: 'New Password',
          hintText: '••••••••',
          controller: _passwordController,
          obscureText: _obscurePassword,
          suffixIcon: IconButton(
            icon: Icon(
              _obscurePassword ? Icons.visibility_off : Icons.visibility,
              size: 12,
              color: const Color(0xff8C8C8C),
            ),
            onPressed: () {
              setState(() {
                _obscurePassword = !_obscurePassword;
              });
            },
          ),
        ),
        const Gap(16),
        InputType(
          labelText: 'Confirm Password',
          hintText: '••••••••',
          controller: _confirmPasswordController,
          obscureText: _obscureConfirmPassword,
          suffixIcon: IconButton(
            icon: Icon(
              _obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
              size: 12,
              color: const Color(0xff8C8C8C),
            ),
            onPressed: () {
              setState(() {
                _obscureConfirmPassword = !_obscureConfirmPassword;
              });
            },
          ),
        ),
        const Gap(24),
        PrimaryButton(
          text: authProvider.isLoading ? 'Resetting...' : 'Reset Password',
          onPressed: authProvider.isLoading
              ? null
              : () async {
                  // Basic validation
                  if (_passwordController.text.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Please enter a password')),
                    );
                    return;
                  }

                  // Password length validation
                  if (_passwordController.text.length < 8) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text(
                              'Password must be at least 8 characters long')),
                    );
                    return;
                  }

                  // Confirm passwords match
                  if (_passwordController.text !=
                      _confirmPasswordController.text) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Passwords do not match')),
                    );
                    return;
                  }

                  // Make sure we have a reset token
                  if (widget.resetToken == null || widget.resetToken!.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text(
                              'Reset token is missing. Please try the forgot password process again.')),
                    );
                    return;
                  }

                  // Call the API to reset the password
                  final result = await authProvider.resetPassword(
                    token: widget.resetToken!,
                    newPassword: _passwordController.text,
                    email: widget.email, // Pass the email if available
                  );

                  // Handle the response
                  if (!mounted) return;

                  if (result['success']) {
                    // Show success message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(result['message'] ??
                              'Password reset successful')),
                    );

                    // Navigate back to login page
                    // Use pushNamedAndRemoveUntil to clear the navigation stack
                    // and prevent going back to the forgot password flow
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  } else {
                    // Show error message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(
                              result['message'] ?? 'Password reset failed')),
                    );
                  }
                },
          width: MediaQuery.of(context).size.width,
          backgroundColor: const Color(0xffFF0000),
          disabledBackgroundColor: const Color(0xFFFF9999),
        ),
      ],
    );
  }
}
