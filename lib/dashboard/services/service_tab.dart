import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:mialamobile/components/search_input.dart';
import 'package:mialamobile/components/text_dropdown.dart';
import 'package:mialamobile/dashboard/home/<USER>/recent_delivery.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mialamobile/utils/delivery_utils.dart';

class ServicesTab extends StatefulWidget {
  const ServicesTab({super.key});

  @override
  State<ServicesTab> createState() => _ServicesTabState();
}

class _ServicesTabState extends State<ServicesTab> {
  @override
  void initState() {
    super.initState();
    // Fetch deliveries when the services tab loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.fetchDeliveries().then((success) {
        if (!success && authProvider.deliveriesError != null && mounted) {
          // Show snackbar for deliveries fetch errors
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Failed to load deliveries: ${authProvider.deliveriesError}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff121212),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'All Deliveries',
                      style: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                    const Gap(20),
                    SearchInput(
                      hintText: 'Search deliveries...',
                      suffixIcon: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: SvgPicture.asset(
                          'assets/icons/search.svg',
                        ),
                      ),
                      focusedBorderColor: Colors.transparent,
                    ),
                    const Gap(5),
                    TextDropdown(
                      value: 'All',
                      items: const [
                        'All',
                        'Delivered',
                        'Pending',
                        'In Transit'
                      ],
                      onChanged: (newValue) {
                        // TODO: Implement filtering by status
                      },
                      dropdownColor: const Color(0xff121212),
                      dropdownWidth: 120,
                      selectedItemColor: const Color(0xffB10303),
                      itemColor: Colors.white,
                    ),
                    const Gap(20),

                    // Loading state
                    if (authProvider.isLoadingDeliveries)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(32.0),
                          child: CircularProgressIndicator(
                            color: Color(0xffB10303),
                          ),
                        ),
                      )
                    // Error state
                    else if (authProvider.deliveriesError != null)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                              color: Colors.red.withValues(alpha: 0.3)),
                        ),
                        child: Column(
                          children: [
                            const Icon(
                              Icons.error_outline,
                              color: Colors.red,
                              size: 48,
                            ),
                            const Gap(8),
                            Text(
                              'Failed to load deliveries',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.red,
                              ),
                            ),
                            const Gap(4),
                            Text(
                              authProvider.deliveriesError!,
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.red.withValues(alpha: 0.8),
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const Gap(12),
                            ElevatedButton(
                              onPressed: () {
                                authProvider.fetchDeliveries();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xffB10303),
                                foregroundColor: Colors.white,
                              ),
                              child: Text(
                                'Retry',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    // Empty state
                    else if (authProvider.deliveries.isEmpty)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(32),
                        child: Column(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/package_delivered.svg',
                              width: 64,
                              height: 64,
                              colorFilter: const ColorFilter.mode(
                                Color(0xff8C8C8C),
                                BlendMode.srcIn,
                              ),
                            ),
                            const Gap(16),
                            Text(
                              'No deliveries found',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            const Gap(8),
                            Text(
                              'Your delivery history will appear here once you start making deliveries.',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: const Color(0xff8C8C8C),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      )
                    // Deliveries list
                    else
                      Column(
                        children: [
                          // Deliveries count
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Row(
                              children: [
                                Text(
                                  'Total: ${authProvider.deliveries.length} deliveries',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xff8C8C8C),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Deliveries list
                          ...authProvider.deliveries.map((delivery) {
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 12),
                              child: RecentDelivery(
                                productName: delivery.productName,
                                trackingId: delivery.deliveryCode,
                                deliveryAddress: delivery.receiverAddress,
                                deliveryDate: DeliveryUtils.formatDeliveryDate(
                                    delivery.dueDate),
                                deliveryTime: DeliveryUtils.formatDeliveryTime(
                                    delivery.uploadDate),
                                deliveryStatus:
                                    DeliveryUtils.formatDeliveryStatus(
                                        delivery.deliveryStatus),
                                statusColor: DeliveryUtils.getStatusColor(
                                    delivery.deliveryStatus),
                                onMarkAsDelivered: delivery.deliveryStatus
                                            .toUpperCase() !=
                                        'DELIVERED'
                                    ? () async {
                                        final scaffoldMessenger =
                                            ScaffoldMessenger.of(context);
                                        final success = await authProvider
                                            .markDeliveryAsDelivered(
                                                delivery.deliveryCode);
                                        if (mounted && !success) {
                                          scaffoldMessenger.showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                  'Failed to mark delivery as delivered. Please try again.'),
                                              backgroundColor: Colors.red,
                                              duration: Duration(seconds: 3),
                                            ),
                                          );
                                        }
                                      }
                                    : null,
                              ),
                            );
                          }),
                        ],
                      ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
