import 'package:mialamobile/api/api_config.dart';

class ApiEndpoints {
  static const String apiPath = ApiConfig.apiPath;

  // Auth endpoints
  static const String login = '$apiPath/auth/login';
  static const String register = '$apiPath/auth/signup';
  static const String logout = '$apiPath/auth/logout';
  static const String otp = '$apiPath/auth/continue-signup';
  static const String resetPassword = '$apiPath/auth/reset-password';
  static const String forgotPassword = '$apiPath/auth/forgot-password';
  static const String verifyOtpForForgotPassword =
      '$apiPath/auth/verify-otp-for-forget-password';
  static const String bank = '$apiPath/auth/banks-list';
  static const String verifyBankAccount = '$apiPath/auth/verify-bank-account';
  static const String states = '$apiPath/auth/states';

  // User endpoints
  static const String userProfile = '$apiPath/user/profile';
  static const String updateProfile = '$apiPath/user/profile/update';

  // DVA endpoints
  static String dvaInfo(String email) => '$apiPath/rider/$email/dva-info';

  // Delivery endpoints
  static const String deliverySummary = '$apiPath/rider/delivery-summary';
  static const String deliveries = '$apiPath/rider/deliveries';
  static String markDelivered(String deliveryCode) =>
      '$apiPath/rider/mark-delivered/$deliveryCode';

  // Notification endpoints
  static String notifications(int userId) =>
      '$apiPath/notify/notifications/$userId';
  static String markNotificationAsRead(int notificationId) =>
      '$apiPath/notify/notifications/mark-read/$notificationId';
}
